<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Human Language Code (HLC) - JSON-Based Software Specification Language
    </title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"
      defer
    ></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#84cc16",
              secondary: "#65a30d",
              accent: "#a3e635",
              lemon: {
                50: "#f7fee7",
                100: "#ecfccb",
                200: "#d9f99d",
                300: "#bef264",
                400: "#a3e635",
                500: "#84cc16",
                600: "#65a30d",
                700: "#4d7c0f",
                800: "#3f6212",
                900: "#365314",
              },
            },
          },
        },
      };
    </script>
    <style>
      .code-block {
        background: #0f172a;
        color: #e2e8f0;
      }
      .json-key {
        color: #84cc16;
      }
      .json-string {
        color: #a3e635;
      }
      .json-value {
        color: #bef264;
      }
      .json-comment {
        color: #94a3b8;
      }
      .json-bracket {
        color: #f1f5f9;
      }
      .gradient-lemon {
        background: linear-gradient(135deg, #84cc16 0%, #a3e635 100%);
      }
      .pattern-dots {
        background-image: radial-gradient(circle, #84cc16 1px, transparent 1px);
        background-size: 20px 20px;
        opacity: 0.1;
      }
    </style>
  </head>
  <body class="bg-gray-50 text-gray-900" x-data="{ sidebarOpen: false }">
    <!-- Mobile menu button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
      <button
        @click="sidebarOpen = !sidebarOpen"
        class="bg-primary text-white p-2 rounded-md shadow-lg hover:bg-secondary transition-colors"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          ></path>
        </svg>
      </button>
    </div>

    <!-- Sidebar -->
    <div
      class="fixed inset-y-0 left-0 z-40 w-72 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 border-r border-gray-200"
      :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
    >
      <div class="flex flex-col h-full">
        <!-- Logo/Header -->
        <div class="p-6 border-b border-gray-200 gradient-lemon">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-md"
            >
              <span class="text-primary font-bold text-xl">{ }</span>
            </div>
            <div>
              <h1 class="text-xl font-bold text-white">HLC</h1>
              <p class="text-sm text-white/80">Human Language Code</p>
            </div>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 p-4 space-y-1 overflow-y-auto">
          <a
            href="#overview"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors font-medium"
          >
            📋 Overview
          </a>
          <a
            href="#core-concepts"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            🏗️ Core Concepts
          </a>
          <a
            href="#object-types"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            🧩 Object Types
          </a>
          <a
            href="#structure"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            🌳 Structure & Linking
          </a>
          <a
            href="#examples"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            💡 Examples
          </a>
          <a
            href="#use-cases"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            🎯 Use Cases
          </a>
          <a
            href="#best-practices"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            ⭐ Best Practices
          </a>
          <a
            href="#tools"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            🛠️ Tools & Ecosystem
          </a>
          <a
            href="#faq"
            @click="sidebarOpen = false"
            class="block px-3 py-2 rounded-md text-gray-700 hover:bg-lemon-50 hover:text-primary transition-colors"
          >
            ❓ FAQ
          </a>
        </nav>

        <!-- Footer -->
        <div class="p-4 border-t border-gray-200">
          <div class="text-xs text-gray-500 text-center">
            <p>HLC Specification v1.0</p>
            <p class="mt-1">JSON-Based Software Specs</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Overlay for mobile -->
    <div
      x-show="sidebarOpen"
      @click="sidebarOpen = false"
      class="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
      x-transition
    ></div>

    <!-- Main content -->
    <div class="lg:ml-72 min-h-screen">
      <main class="max-w-5xl mx-auto px-6 py-8">
        <!-- Hero Section -->
        <section id="overview" class="mb-16">
          <div
            class="relative overflow-hidden gradient-lemon rounded-2xl p-8 mb-8 text-white"
          >
            <div class="pattern-dots absolute inset-0"></div>
            <div class="relative z-10">
              <div class="flex items-center space-x-4 mb-6">
                <div
                  class="w-16 h-16 bg-white rounded-xl flex items-center justify-center shadow-lg"
                >
                  <span class="text-primary font-bold text-2xl">{ }</span>
                </div>
                <div>
                  <h1 class="text-4xl font-bold mb-2">Human Language Code</h1>
                  <p class="text-xl opacity-90">
                    JSON-Based Software Specification Intermediate Language
                  </p>
                </div>
              </div>
              <p class="text-lg opacity-90 max-w-3xl">
                HLC bridges the gap between human-readable requirements and
                structured programming by defining software specifications using
                hierarchical JSON objects. Transform vague ideas into
                machine-readable specifications.
              </p>
            </div>
          </div>

          <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div
              class="bg-white p-6 rounded-xl shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-100 rounded-lg flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-gray-800">
                Structured Specifications
              </h3>
              <p class="text-gray-600">
                Convert natural language requirements into organized JSON
                hierarchies that machines can understand and process.
              </p>
            </div>
            <div
              class="bg-white p-6 rounded-xl shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-100 rounded-lg flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-gray-800">
                Intermediate Layer
              </h3>
              <p class="text-gray-600">
                Acts as a middle layer between human ideas and technical
                implementation, making complex projects manageable.
              </p>
            </div>
            <div
              class="bg-white p-6 rounded-xl shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-100 rounded-lg flex items-center justify-center mb-4"
              >
                <svg
                  class="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-gray-800">
                Tool Integration
              </h3>
              <p class="text-gray-600">
                Enable automated code generation, documentation creation, and
                workflow management from your specifications.
              </p>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Why HLC?</h3>
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-semibold text-primary mb-2">🎯 Clarity</h4>
                <p class="text-gray-600 mb-4">
                  Translates vague requirements into structured, unambiguous
                  JSON specifications.
                </p>

                <h4 class="font-semibold text-primary mb-2">📈 Scalability</h4>
                <p class="text-gray-600">
                  Breaks down complex projects into nested, manageable
                  components.
                </p>
              </div>
              <div>
                <h4 class="font-semibold text-primary mb-2">
                  🤖 Machine Readable
                </h4>
                <p class="text-gray-600 mb-4">
                  Tools can parse HLC to auto-generate code, documentation, or
                  workflows.
                </p>

                <h4 class="font-semibold text-primary mb-2">🔄 Standardized</h4>
                <p class="text-gray-600">
                  Provides a consistent format for software specification across
                  teams and projects.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Core Concepts Section -->
        <section id="core-concepts" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">
            🏗️ Core Concepts
          </h2>

          <div class="space-y-8">
            <!-- Mandatory Structure -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >1</span
                >
                Mandatory Two-Object Structure
              </h3>
              <p class="text-gray-600 mb-4">
                Every HLC specification must start with exactly two top-level
                objects in an array:
              </p>

              <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-primary mb-2">
                    Entry-Level Object
                  </h4>
                  <p class="text-gray-700 text-sm">
                    Defines the project's structure, framework, and global
                    rules. Sets up the foundation for how the entire project
                    should be organized.
                  </p>
                </div>
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-primary mb-2">
                    Top-Level Object
                  </h4>
                  <p class="text-gray-700 text-sm">
                    Specifies the type of software being built (e.g., mobile
                    app, web service, calculator). Defines the main
                    functionality.
                  </p>
                </div>
              </div>

              <div class="mt-4 code-block rounded-lg p-4">
                <code class="text-sm">
                  <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;<span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-comment"
                    >/* Entry-Level Object - Project Structure */</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"name"</span>:
                  <span class="json-string">"DJANGO-STYLE"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"type"</span>:
                  <span class="json-string">"entry_level"</span><br />
                  &nbsp;&nbsp;<span class="json-bracket">}</span>,<br />
                  &nbsp;&nbsp;<span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-comment"
                    >/* Top-Level Object - Software Type */</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"name"</span>:
                  <span class="json-string">"CALCULATOR-APP"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"type"</span>:
                  <span class="json-string">"top_level"</span><br />
                  &nbsp;&nbsp;<span class="json-bracket">}</span><br />
                  <span class="json-bracket">]</span>
                </code>
              </div>
            </div>

            <!-- Required Fields -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >2</span
                >
                Required Object Fields
              </h3>
              <p class="text-gray-600 mb-4">
                Every HLC object must contain these three essential fields:
              </p>

              <div class="space-y-4">
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white font-bold"
                  >
                    N
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-800">name</h4>
                    <p class="text-gray-600 text-sm">
                      A unique identifier for the object (e.g., "DJANGO-STYLE",
                      "USER_AUTH")
                    </p>
                  </div>
                </div>
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center text-white font-bold"
                  >
                    T
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-800">type</h4>
                    <p class="text-gray-600 text-sm">
                      The role of the object (entry_level, top_level, feature,
                      control, directive, literal)
                    </p>
                  </div>
                </div>
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center text-white font-bold"
                  >
                    P
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-800">prompt</h4>
                    <p class="text-gray-600 text-sm">
                      A natural-language explanation of what the object does or
                      represents
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold text-gray-800 mb-2">
                  Optional Field:
                </h4>
                <div class="flex items-start space-x-4">
                  <div
                    class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center text-white font-bold"
                  >
                    L
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-800">linkers</h4>
                    <p class="text-gray-600 text-sm">
                      An array of child objects that provide additional detail
                      or refinement to the parent object
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Hierarchical Nature -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >3</span
                >
                Hierarchical Organization
              </h3>
              <p class="text-gray-600 mb-4">
                HLC organizes specifications in a tree-like structure where
                objects can contain child objects through linkers:
              </p>

              <div
                class="bg-gradient-to-r from-lemon-50 to-lemon-100 p-6 rounded-lg border border-lemon-200"
              >
                <div class="text-center">
                  <div
                    class="inline-block bg-primary text-white px-4 py-2 rounded-lg font-semibold mb-4"
                  >
                    Entry-Level Object
                  </div>
                  <div class="flex justify-center space-x-8 mb-4">
                    <div class="text-center">
                      <div class="w-2 h-8 bg-gray-300 mx-auto mb-2"></div>
                      <div
                        class="inline-block bg-secondary text-white px-3 py-1 rounded text-sm"
                      >
                        Directive
                      </div>
                    </div>
                    <div class="text-center">
                      <div class="w-2 h-8 bg-gray-300 mx-auto mb-2"></div>
                      <div
                        class="inline-block bg-secondary text-white px-3 py-1 rounded text-sm"
                      >
                        Feature
                      </div>
                    </div>
                  </div>
                  <div class="flex justify-center">
                    <div class="text-center">
                      <div class="w-2 h-8 bg-gray-300 mx-auto mb-2"></div>
                      <div
                        class="inline-block bg-accent text-white px-3 py-1 rounded text-sm"
                      >
                        Literal
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Object Types Section -->
        <section id="object-types" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">🧩 Object Types</h2>

          <div class="grid md:grid-cols-2 gap-6">
            <!-- Entry Level -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  EL
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">
                    Entry-Level
                  </h3>
                  <p class="text-sm text-gray-500">type: "entry_level"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Sets up project-wide structure, framework choices, and global
                rules. Always appears first in the array.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Django project structure, React app
                  setup, microservices architecture
                </p>
              </div>
            </div>

            <!-- Top Level -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  TL
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">Top-Level</h3>
                  <p class="text-sm text-gray-500">type: "top_level"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Defines the main software type and primary functionality. Always
                appears second in the array.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Calculator app, blog platform,
                  e-commerce site, game engine
                </p>
              </div>
            </div>

            <!-- Feature -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  F
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">Feature</h3>
                  <p class="text-sm text-gray-500">type: "feature"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Describes specific functionality or capabilities. Linked to
                parent objects through linkers.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> User authentication, file upload,
                  search functionality, payment processing
                </p>
              </div>
            </div>

            <!-- Control -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-lemon-600 rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  C
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">Control</h3>
                  <p class="text-sm text-gray-500">type: "control"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Dictates behavior, timing, or operational parameters. Controls
                how features work.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Auto-save every 5 seconds, rate
                  limiting, cache expiration, retry logic
                </p>
              </div>
            </div>

            <!-- Directive -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-lemon-700 rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  D
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">Directive</h3>
                  <p class="text-sm text-gray-500">type: "directive"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Enforces rules, standards, or requirements. Often nested under
                other objects via linkers.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Use HTTPS only, follow REST API
                  standards, implement GDPR compliance
                </p>
              </div>
            </div>

            <!-- Literal -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-lemon-800 rounded-lg flex items-center justify-center text-white font-bold mr-4"
                >
                  L
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">Literal</h3>
                  <p class="text-sm text-gray-500">type: "literal"</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">
                Provides fixed values or concrete specifications. Cannot have
                linkers (terminal nodes).
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Version 4.2, port 8080, database
                  name "users_db", API key format
                </p>
              </div>
            </div>
          </div>

          <!-- Object Relationships -->
          <div
            class="mt-8 bg-white rounded-xl shadow-md border border-gray-200 p-6"
          >
            <h3 class="text-xl font-semibold mb-4 text-gray-800">
              Object Relationships & Rules
            </h3>
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-semibold text-primary mb-3">
                  ✅ Allowed Relationships
                </h4>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                    >Entry-Level → Directive, Feature, Control
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                    >Top-Level → Feature, Control, Literal
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                    >Feature → Control, Directive, Literal
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                    >Directive → Literal, Control
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                    >Control → Literal
                  </li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold text-red-600 mb-3">❌ Restrictions</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span
                    >Root objects cannot link to each other
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span
                    >Literals cannot have linkers
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>No
                    circular references allowed
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span
                    >Entry-Level must be first
                  </li>
                  <li class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span
                    >Top-Level must be second
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <!-- Structure & Linking Section -->
        <section id="structure" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">
            🌳 Structure & Linking
          </h2>

          <div class="space-y-8">
            <!-- Linkers Explanation -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Understanding Linkers
              </h3>
              <p class="text-gray-600 mb-4">
                Linkers are the mechanism that connects parent objects to their
                children, creating the hierarchical structure that makes HLC
                powerful.
              </p>

              <div
                class="bg-lemon-50 p-4 rounded-lg border border-lemon-200 mb-4"
              >
                <h4 class="font-semibold text-primary mb-2">
                  Linker Structure
                </h4>
                <p class="text-gray-700 text-sm mb-3">
                  Each linker contains three essential components:
                </p>
                <ul class="space-y-2 text-sm text-gray-700">
                  <li>
                    <strong>name:</strong> The role or relationship type (e.g.,
                    "framework", "authentication", "database")
                  </li>
                  <li>
                    <strong>description:</strong> How the child affects or
                    relates to the parent
                  </li>
                  <li>
                    <strong>value:</strong> The actual child object with its own
                    name, type, and prompt
                  </li>
                </ul>
              </div>

              <div class="code-block rounded-lg p-4">
                <code class="text-sm">
                  <span class="json-string">"linkers"</span>:
                  <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;<span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"name"</span>:
                  <span class="json-string">"framework"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key"
                    >"description"</span
                  >: <span class="json-string">"Project framework"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"value"</span>:
                  <span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key"
                    >"name"</span
                  >: <span class="json-string">"DJANGO"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key"
                    >"type"</span
                  >: <span class="json-string">"directive"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key"
                    >"prompt"</span
                  >: <span class="json-string">"Use Django framework"</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket">}</span
                  ><br />
                  &nbsp;&nbsp;<span class="json-bracket">}</span><br />
                  <span class="json-bracket">]</span>
                </code>
              </div>
            </div>

            <!-- Hierarchy Rules -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Hierarchy Rules
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-primary mb-3">
                    🔗 Linking Rules
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Root objects (Entry-Level, Top-Level) cannot link to each
                      other
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Objects can have multiple linkers (multiple children)
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Linkers create parent-child relationships
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Children inherit context from parents
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-semibold text-red-600 mb-3">
                    🚫 Restrictions
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"
                      ></span
                      >Literals cannot have linkers (they are terminal)
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"
                      ></span
                      >No circular references allowed
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"
                      ></span
                      >Maximum depth should be reasonable (3-5 levels)
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-red-500 rounded-full mr-2 mt-2"
                      ></span
                      >Each linker name should be unique within its parent
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Examples Section -->
        <section id="examples" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">💡 Examples</h2>

          <div class="space-y-8">
            <!-- Complete Example -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Complete HLC Specification: Django Calculator App
              </h3>
              <p class="text-gray-600 mb-4">
                This example shows a complete HLC specification for building a
                calculator web application using Django:
              </p>

              <div class="code-block rounded-lg p-4 text-xs overflow-x-auto">
                <code>
                  <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;<span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"name"</span>:
                  <span class="json-string">"DJANGO-STYLE"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"type"</span>:
                  <span class="json-string">"entry_level"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"prompt"</span
                  >:
                  <span class="json-string"
                    >"Create this project using Django style project
                    structure."</span
                  >,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"linkers"</span
                  >: <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >{</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"rules"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"description"</span
                  >: <span class="json-string">"Project rules"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"value"</span
                  >: <span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"DJANGO-STYLE-RULE"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"type"</span
                  >: <span class="json-string">"directive"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"prompt"</span
                  >:
                  <span class="json-string"
                    >"Separate settings into local, production and
                    testing."</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >}</span
                  >,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >{</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"framework"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"description"</span
                  >: <span class="json-string">"Project framework"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"value"</span
                  >: <span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"DJANGO"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"type"</span
                  >: <span class="json-string">"directive"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"prompt"</span
                  >:
                  <span class="json-string"
                    >"Use Python Django as the framework."</span
                  >,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"linkers"</span
                  >: <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >{</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"version"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"description"</span
                  >: <span class="json-string">"Django version"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"value"</span
                  >: <span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"VERSION"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"type"</span
                  >: <span class="json-string">"literal"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"prompt"</span
                  >:
                  <span class="json-string"
                    >"Use the latest version of Django."</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >]</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket">]</span
                  ><br />
                  &nbsp;&nbsp;<span class="json-bracket">}</span>,<br />
                  &nbsp;&nbsp;<span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"name"</span>:
                  <span class="json-string">"CALCULATOR-APP"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"type"</span>:
                  <span class="json-string">"top_level"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"prompt"</span
                  >:
                  <span class="json-string"
                    >"Create a calculator web application."</span
                  >,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-key">"linkers"</span
                  >: <span class="json-bracket">[</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >{</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"pages"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"description"</span
                  >: <span class="json-string">"App pages"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"value"</span
                  >: <span class="json-bracket">{</span><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"name"</span
                  >: <span class="json-string">"PAGES"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"type"</span
                  >: <span class="json-string">"literal"</span>,<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-key"
                    >"prompt"</span
                  >:
                  <span class="json-string"
                    >"Single page calculator interface."</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                    class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket"
                    >}</span
                  ><br />
                  &nbsp;&nbsp;&nbsp;&nbsp;<span class="json-bracket">]</span
                  ><br />
                  &nbsp;&nbsp;<span class="json-bracket">}</span><br />
                  <span class="json-bracket">]</span>
                </code>
              </div>
            </div>

            <!-- What This Means -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                What This Specification Means
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-primary mb-3">
                    📋 Project Structure
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Use Django framework (latest version)
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Split settings into local, production, testing
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-primary rounded-full mr-2 mt-2"
                      ></span
                      >Follow Django project structure conventions
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-semibold text-secondary mb-3">
                    🎯 Application Features
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2 mt-2"
                      ></span
                      >Calculator web application
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2 mt-2"
                      ></span
                      >Single page interface
                    </li>
                    <li class="flex items-start">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2 mt-2"
                      ></span
                      >Web-based calculator functionality
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Use Cases Section -->
        <section id="use-cases" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">🎯 Use Cases</h2>

          <div class="grid md:grid-cols-2 gap-6">
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                🏢
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                Enterprise Software Planning
              </h3>
              <p class="text-gray-600 mb-4">
                Define complex enterprise applications with multiple
                microservices, databases, and integrations.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> E-commerce platform with user
                  management, payment processing, inventory tracking, and
                  analytics
                </p>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                🤖
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                AI Code Generation
              </h3>
              <p class="text-gray-600 mb-4">
                Provide structured input for AI systems to generate accurate
                code based on detailed specifications.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Feed HLC specs to GPT models for
                  generating Django, React, or mobile app code
                </p>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                📋
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                Project Documentation
              </h3>
              <p class="text-gray-600 mb-4">
                Create living documentation that evolves with your project and
                can be automatically processed.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Generate API documentation,
                  architecture diagrams, and deployment guides from HLC specs
                </p>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-600 rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                👥
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                Team Communication
              </h3>
              <p class="text-gray-600 mb-4">
                Bridge the gap between business stakeholders and technical teams
                with clear, structured requirements.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Product managers define features in
                  HLC, developers implement based on the structured specs
                </p>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-700 rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                🔄
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                DevOps Automation
              </h3>
              <p class="text-gray-600 mb-4">
                Generate deployment configurations, CI/CD pipelines, and
                infrastructure as code from specifications.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Auto-generate Docker files,
                  Kubernetes manifests, and Terraform configurations
                </p>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
            >
              <div
                class="w-12 h-12 bg-lemon-800 rounded-lg flex items-center justify-center text-white font-bold mb-4"
              >
                🎓
              </div>
              <h3 class="text-lg font-semibold mb-3 text-gray-800">
                Educational Tools
              </h3>
              <p class="text-gray-600 mb-4">
                Teach software architecture and system design using structured,
                visual specifications.
              </p>
              <div class="bg-lemon-50 p-3 rounded-lg border border-lemon-200">
                <p class="text-sm text-gray-700">
                  <strong>Example:</strong> Computer science courses using HLC
                  to teach system design and software architecture patterns
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Best Practices Section -->
        <section id="best-practices" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">
            ⭐ Best Practices
          </h2>

          <div class="space-y-6">
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >1</span
                >
                Start Simple, Build Complexity
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-primary mb-2">✅ Do</h4>
                  <ul class="space-y-1 text-sm text-gray-600">
                    <li>
                      • Begin with basic Entry-Level and Top-Level objects
                    </li>
                    <li>• Add features incrementally through linkers</li>
                    <li>• Keep initial specifications focused and clear</li>
                    <li>• Use descriptive names and prompts</li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-semibold text-red-600 mb-2">❌ Don't</h4>
                  <ul class="space-y-1 text-sm text-gray-600">
                    <li>• Create deeply nested hierarchies from the start</li>
                    <li>• Use vague or generic object names</li>
                    <li>• Skip the mandatory two-object structure</li>
                    <li>• Over-complicate initial specifications</li>
                  </ul>
                </div>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >2</span
                >
                Naming Conventions
              </h3>
              <div class="grid md:grid-cols-3 gap-4">
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-primary mb-2">Object Names</h4>
                  <p class="text-sm text-gray-700 mb-2">
                    Use UPPERCASE with hyphens
                  </p>
                  <code class="text-xs bg-white p-1 rounded">DJANGO-STYLE</code
                  ><br />
                  <code class="text-xs bg-white p-1 rounded">USER-AUTH</code>
                </div>
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-secondary mb-2">
                    Linker Names
                  </h4>
                  <p class="text-sm text-gray-700 mb-2">
                    Use lowercase descriptive terms
                  </p>
                  <code class="text-xs bg-white p-1 rounded">framework</code
                  ><br />
                  <code class="text-xs bg-white p-1 rounded"
                    >authentication</code
                  >
                </div>
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-accent mb-2">Prompts</h4>
                  <p class="text-sm text-gray-700 mb-2">
                    Clear, actionable sentences
                  </p>
                  <code class="text-xs bg-white p-1 rounded"
                    >"Use Django framework"</code
                  >
                </div>
              </div>
            </div>

            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3
                class="text-xl font-semibold mb-4 text-gray-800 flex items-center"
              >
                <span
                  class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center text-white text-sm font-bold mr-3"
                  >3</span
                >
                Validation Checklist
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-primary mb-3">
                    Structure Validation
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Entry-Level object
                      is first
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Top-Level object is
                      second
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> All objects have
                      name, type, prompt
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Literals have no
                      linkers
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> No circular
                      references
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-semibold text-secondary mb-3">
                    Content Validation
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Names are
                      descriptive and unique
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Types match object
                      purposes
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Prompts are clear
                      and actionable
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Linker
                      relationships make sense
                    </li>
                    <li class="flex items-center">
                      <input type="checkbox" class="mr-2" /> Hierarchy depth is
                      reasonable
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Tools & Ecosystem Section -->
        <section id="tools" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">
            🛠️ Tools & Ecosystem
          </h2>

          <div class="space-y-8">
            <!-- Core Tools -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Core Tools
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-primary mb-2">
                    🔍 HLC Validator
                  </h4>
                  <p class="text-gray-700 text-sm mb-3">
                    Validates HLC specifications against the official schema and
                    rules.
                  </p>
                  <div class="code-block rounded p-2 text-xs">
                    <code
                      >npm install -g hlc-validator<br />hlc-validate
                      spec.json</code
                    >
                  </div>
                </div>
                <div class="bg-lemon-50 p-4 rounded-lg border border-lemon-200">
                  <h4 class="font-semibold text-primary mb-2">
                    📊 HLC Visualizer
                  </h4>
                  <p class="text-gray-700 text-sm mb-3">
                    Generates visual diagrams and flowcharts from HLC
                    specifications.
                  </p>
                  <div class="code-block rounded p-2 text-xs">
                    <code
                      >hlc-viz --input spec.json<br />--output diagram.svg</code
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Code Generators -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Code Generators
              </h3>
              <div class="grid md:grid-cols-3 gap-4">
                <div
                  class="text-center p-4 bg-gradient-to-br from-lemon-50 to-lemon-100 rounded-lg border border-lemon-200"
                >
                  <div
                    class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white font-bold mx-auto mb-3"
                  >
                    🐍
                  </div>
                  <h4 class="font-semibold text-gray-800 mb-2">
                    Django Generator
                  </h4>
                  <p class="text-sm text-gray-600">
                    Generate Django projects, models, views, and APIs from HLC
                    specs.
                  </p>
                </div>
                <div
                  class="text-center p-4 bg-gradient-to-br from-lemon-50 to-lemon-100 rounded-lg border border-lemon-200"
                >
                  <div
                    class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center text-white font-bold mx-auto mb-3"
                  >
                    ⚛️
                  </div>
                  <h4 class="font-semibold text-gray-800 mb-2">
                    React Generator
                  </h4>
                  <p class="text-sm text-gray-600">
                    Create React components, hooks, and state management from
                    specifications.
                  </p>
                </div>
                <div
                  class="text-center p-4 bg-gradient-to-br from-lemon-50 to-lemon-100 rounded-lg border border-lemon-200"
                >
                  <div
                    class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center text-white font-bold mx-auto mb-3"
                  >
                    📱
                  </div>
                  <h4 class="font-semibold text-gray-800 mb-2">
                    Mobile Generator
                  </h4>
                  <p class="text-sm text-gray-600">
                    Generate React Native and Flutter apps with navigation and
                    features.
                  </p>
                </div>
              </div>
            </div>

            <!-- Integration Tools -->
            <div
              class="bg-white rounded-xl shadow-md border border-gray-200 p-6"
            >
              <h3 class="text-xl font-semibold mb-4 text-gray-800">
                Integration Tools
              </h3>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-primary mb-3">
                    🔌 IDE Extensions
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                      <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                      >VS Code HLC Extension
                    </li>
                    <li class="flex items-center">
                      <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                      >IntelliJ HLC Plugin
                    </li>
                    <li class="flex items-center">
                      <span class="w-2 h-2 bg-primary rounded-full mr-2"></span
                      >Vim HLC Syntax Highlighting
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-semibold text-secondary mb-3">
                    🔄 CI/CD Integration
                  </h4>
                  <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2"
                      ></span
                      >GitHub Actions Workflow
                    </li>
                    <li class="flex items-center">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2"
                      ></span
                      >Jenkins Pipeline Plugin
                    </li>
                    <li class="flex items-center">
                      <span
                        class="w-2 h-2 bg-secondary rounded-full mr-2"
                      ></span
                      >GitLab CI Templates
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="mb-16">
          <h2 class="text-3xl font-bold mb-8 text-gray-800">
            ❓ Frequently Asked Questions
          </h2>

          <div class="space-y-4" x-data="{ openFaq: null }">
            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 1 ? null : 1"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    What makes HLC different from other specification formats?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 1 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 1" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  HLC is specifically designed as an intermediate language
                  between human requirements and machine code. Unlike
                  traditional documentation formats, HLC enforces a strict
                  hierarchical structure with mandatory Entry-Level and
                  Top-Level objects, making it both human-readable and
                  machine-parseable. This enables automated code generation,
                  validation, and tooling integration.
                </p>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 2 ? null : 2"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    Can I use HLC for existing projects?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 2 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 2" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  Absolutely! HLC can be used to document and specify existing
                  projects. You can create HLC specifications that describe your
                  current architecture, then use them for documentation,
                  onboarding new team members, or planning future enhancements.
                  Many teams start by creating HLC specs for their existing
                  systems before using them for new development.
                </p>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 3 ? null : 3"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    How deep can the hierarchy go?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 3 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 3" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  While there's no hard technical limit, we recommend keeping
                  hierarchies to 3-5 levels deep for maintainability and
                  clarity. Deeper hierarchies can become difficult to understand
                  and maintain. If you find yourself needing deeper nesting,
                  consider breaking your specification into multiple related HLC
                  files or restructuring your object relationships.
                </p>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 4 ? null : 4"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    Is HLC suitable for non-technical stakeholders?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 4 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 4" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  Yes! While HLC uses JSON syntax, the focus on natural language
                  prompts and clear hierarchical structure makes it accessible
                  to non-technical stakeholders. Product managers, business
                  analysts, and designers can contribute to HLC specifications
                  by focusing on the "prompt" fields, which use plain English to
                  describe requirements and functionality.
                </p>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 5 ? null : 5"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    How do I get started with HLC?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 5 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 5" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  Start simple! Begin with the mandatory two-object structure
                  (Entry-Level and Top-Level), then gradually add features
                  through linkers. Use the examples in this documentation as
                  templates, and don't hesitate to start with basic
                  specifications that you can expand over time. The HLC
                  validator tool can help ensure your specifications follow the
                  correct format.
                </p>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-200">
              <button
                @click="openFaq = openFaq === 6 ? null : 6"
                class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary rounded-xl"
              >
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">
                    Can HLC specifications be version controlled?
                  </h3>
                  <svg
                    class="w-5 h-5 transform transition-transform text-primary"
                    :class="openFaq === 6 ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </button>
              <div x-show="openFaq === 6" x-transition class="px-6 pb-6">
                <p class="text-gray-600">
                  Absolutely! Since HLC specifications are JSON files, they work
                  perfectly with Git and other version control systems. You can
                  track changes, create branches for different feature
                  specifications, merge specifications from different team
                  members, and maintain a complete history of your project's
                  evolution. Many teams store HLC specs alongside their code
                  repositories.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  </body>
</html>

